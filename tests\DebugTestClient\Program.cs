﻿using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("调试MQTT测试客户端启动...");

        try
        {
            Console.WriteLine("尝试连接到 127.0.0.1:1883...");

            using var client = new TcpClient();
            await client.ConnectAsync("127.0.0.1", 1883);

            Console.WriteLine("✅ TCP连接成功建立");

            var stream = client.GetStream();

            // 发送一个非常简单的CONNECT包
            var connectPacket = CreateMinimalConnectPacket();
            Console.WriteLine($"发送CONNECT包 ({connectPacket.Length} 字节): {BitConverter.ToString(connectPacket)}");

            // 逐字节分析数据包
            Console.WriteLine("数据包分析:");
            Console.WriteLine($"  包类型: 0x{connectPacket[0]:X2}");
            Console.WriteLine($"  剩余长度: {connectPacket[1]}");
            Console.WriteLine($"  协议名称长度: {(connectPacket[2] << 8) | connectPacket[3]}");
            Console.WriteLine($"  协议名称: {Encoding.UTF8.GetString(connectPacket, 4, 4)}");
            Console.WriteLine($"  协议版本: {connectPacket[8]}");
            Console.WriteLine($"  连接标志: 0x{connectPacket[9]:X2}");
            Console.WriteLine($"  Keep Alive: {(connectPacket[10] << 8) | connectPacket[11]}");
            Console.WriteLine($"  客户端ID长度: {(connectPacket[12] << 8) | connectPacket[13]}");
            Console.WriteLine($"  客户端ID: {Encoding.UTF8.GetString(connectPacket, 14, connectPacket.Length - 14)}");

            await stream.WriteAsync(connectPacket);
            Console.WriteLine("✅ CONNECT包已发送");

            // 等待响应
            await Task.Delay(2000);

            Console.WriteLine("等待2秒后关闭连接");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 发生异常: {ex.Message}");
            Console.WriteLine($"异常类型: {ex.GetType().Name}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"内部异常: {ex.InnerException.Message}");
            }
        }

        Console.WriteLine("测试完成");
    }

    static byte[] CreateMinimalConnectPacket()
    {
        var packet = new List<byte>();

        // 固定头部
        packet.Add(0x10); // CONNECT包类型

        // 可变头部和载荷
        var payload = new List<byte>();

        // 协议名称 "MQTT"
        payload.Add(0x00); // 长度高字节
        payload.Add(0x04); // 长度低字节
        payload.AddRange(Encoding.UTF8.GetBytes("MQTT"));

        // 协议版本
        payload.Add(0x04); // MQTT 3.1.1

        // 连接标志
        payload.Add(0x00); // 无特殊标志

        // Keep Alive
        payload.Add(0x00); // 高字节
        payload.Add(0x3C); // 低字节 (60秒)

        // 客户端ID "Test"
        var clientId = "Test";
        var clientIdBytes = Encoding.UTF8.GetBytes(clientId);
        payload.Add(0x00); // 长度高字节
        payload.Add((byte)clientIdBytes.Length); // 长度低字节
        payload.AddRange(clientIdBytes);

        // 剩余长度
        packet.Add((byte)payload.Count);
        packet.AddRange(payload);

        return packet.ToArray();
    }
}
