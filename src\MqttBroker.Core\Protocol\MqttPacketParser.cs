using System;
using System.Buffers;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 数据包解析器实现
    /// </summary>
    public class MqttPacketParser : IMqttPacketParser
    {
        private readonly ILogger<MqttPacketParser> _logger;

        /// <summary>
        /// 初始化 MQTT 数据包解析器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public MqttPacketParser(ILogger<MqttPacketParser> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 解析 MQTT 数据包
        /// </summary>
        /// <param name="buffer">包含数据包数据的缓冲区</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>解析后的数据包，如果解析失败则返回 null</returns>
        public IMqttPacket? ParsePacket(ReadOnlySequence<byte> buffer, MqttProtocolVersion protocolVersion)
        {
            try
            {
                if (!TryParsePacketHeader(buffer, out var packetLength, out var headerLength))
                {
                    _logger.LogDebug("Failed to parse packet header");
                    return null;
                }

                if (buffer.Length < packetLength)
                {
                    _logger.LogDebug("Buffer does not contain complete packet. Expected: {ExpectedLength}, Actual: {ActualLength}",
                        packetLength, buffer.Length);
                    return null;
                }

                // 只处理单个数据包的数据，避免缓冲区大小不匹配问题
                var packetBuffer = buffer.Slice(0, packetLength);
                var reader = new MqttBinaryReader(packetBuffer);

                // 读取固定头部
                var firstByte = reader.ReadByte();
                var packetType = (MqttPacketType)((firstByte & MqttProtocolConstants.PacketTypeMasks.PacketType) >> 4);
                var flags = (byte)(firstByte & MqttProtocolConstants.PacketTypeMasks.Flags);

                // 读取剩余长度
                var remainingLength = reader.ReadVariableByteInteger();

                // 创建一个新的reader，指向可变头部的开始位置（跳过固定头部）
                var fixedHeaderLength = (int)(reader.Position);
                var payloadBuffer = packetBuffer.Slice(fixedHeaderLength);
                var payloadReader = new MqttBinaryReader(payloadBuffer);

                _logger.LogInformation("Parsing packet: Type={PacketType}, Flags={Flags:X2}, RemainingLength={RemainingLength}, BufferLength={BufferLength}, FixedHeaderLength={FixedHeaderLength}, PayloadReaderRemaining={PayloadReaderRemaining}",
                    packetType, flags, remainingLength, buffer.Length, fixedHeaderLength, payloadReader.Remaining);

                // 输出 payloadBuffer 的内容用于调试
                if (payloadReader.Remaining > 0)
                {
                    var payloadDebugBytes = new byte[Math.Min(payloadReader.Remaining, 30)];
                    var payloadDebugSlice = payloadReader.GetSlice((int)Math.Min(payloadReader.Remaining, 30));
                    payloadDebugSlice.CopyTo(payloadDebugBytes);
                    _logger.LogInformation("PayloadBuffer content (first 30 bytes): {PayloadBufferHex}", BitConverter.ToString(payloadDebugBytes));
                }

                // 输出缓冲区内容用于调试
                var debugBytes = new byte[Math.Min(buffer.Length, 50)];
                buffer.Slice(0, Math.Min(buffer.Length, 50)).CopyTo(debugBytes);
                _logger.LogInformation("Buffer content (first 50 bytes): {BufferHex}", BitConverter.ToString(debugBytes));

                // 详细分析前几个字节
                if (buffer.Length >= 4)
                {
                    var firstBytes = new byte[4];
                    buffer.Slice(0, 4).CopyTo(firstBytes);
                    _logger.LogInformation("First 4 bytes: {Byte0:X2} {Byte1:X2} {Byte2:X2} {Byte3:X2}",
                        firstBytes[0], firstBytes[1], firstBytes[2], firstBytes[3]);
                }

                // 验证剩余长度与实际数据的一致性
                // payloadReader.Remaining 是可变头部和有效载荷的字节数
                // 这应该等于剩余长度字段的值
                if (payloadReader.Remaining != remainingLength)
                {
                    _logger.LogWarning("Data length mismatch. Expected remaining: {Expected}, Actual remaining: {Actual}, BufferLength: {BufferLength}, FixedHeaderLength: {FixedHeaderLength}",
                        remainingLength, payloadReader.Remaining, buffer.Length, fixedHeaderLength);

                    // 输出缓冲区内容用于调试
                    var bufferBytes = new byte[buffer.Length];
                    buffer.CopyTo(bufferBytes);
                    _logger.LogDebug("Buffer content: {BufferHex}", BitConverter.ToString(bufferBytes));

                    return null;
                }

                // 根据数据包类型解析
                return packetType switch
                {
                    MqttPacketType.Connect => ParseConnectPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.ConnAck => ParseConnAckPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.Publish => ParsePublishPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.PubAck => ParsePubAckPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.PubRec => ParsePubRecPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.PubRel => ParsePubRelPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.PubComp => ParsePubCompPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.Subscribe => ParseSubscribePacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.SubAck => ParseSubAckPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.Unsubscribe => ParseUnsubscribePacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.UnsubAck => ParseUnsubAckPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.PingReq => ParsePingReqPacket(payloadReader, flags),
                    MqttPacketType.PingResp => ParsePingRespPacket(payloadReader, flags),
                    MqttPacketType.Disconnect => ParseDisconnectPacket(payloadReader, flags, protocolVersion),
                    MqttPacketType.Auth => ParseAuthPacket(payloadReader, flags, protocolVersion),
                    _ => null // 返回 null 而不是抛出异常
                };
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid MQTT packet format: {Message}", ex.Message);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error parsing MQTT packet");
                return null;
            }
        }

        /// <summary>
        /// 尝试解析数据包头部以确定数据包长度
        /// </summary>
        /// <param name="buffer">包含数据包头部的缓冲区</param>
        /// <param name="packetLength">输出参数：数据包总长度</param>
        /// <param name="headerLength">输出参数：头部长度</param>
        /// <returns>如果成功解析头部则返回 true，否则返回 false</returns>
        public bool TryParsePacketHeader(ReadOnlySequence<byte> buffer, out int packetLength, out int headerLength)
        {
            packetLength = 0;
            headerLength = 0;

            if (buffer.Length < 2) // 至少需要 2 字节（类型 + 最小剩余长度）
            {
                _logger.LogDebug("TryParsePacketHeader: Buffer too small: {BufferLength}", buffer.Length);
                return false;
            }

            // 输出缓冲区内容用于调试
            var debugBytes = new byte[Math.Min(buffer.Length, 20)];
            buffer.Slice(0, Math.Min(buffer.Length, 20)).CopyTo(debugBytes);
            _logger.LogInformation("TryParsePacketHeader: Buffer content (first 20 bytes): {BufferHex}", BitConverter.ToString(debugBytes));

            try
            {
                var reader = new MqttBinaryReader(buffer);

                // 读取第一个字节（数据包类型和标志）
                var firstByte = reader.ReadByte();
                headerLength = 1;

                // 验证数据包类型是否有效
                var packetType = (MqttPacketType)((firstByte & MqttProtocolConstants.PacketTypeMasks.PacketType) >> 4);
                if (!IsValidPacketType(packetType))
                {
                    _logger.LogWarning("Invalid packet type: {PacketType}", packetType);
                    return false;
                }

                // 读取剩余长度
                int remainingLength = 0;
                int multiplier = 1;
                int byteCount = 0;

                while (true)
                {
                    if (byteCount >= 4 || !reader.HasBytes(1))
                        return false;

                    var encodedByte = reader.ReadByte();
                    headerLength++;

                    remainingLength += (encodedByte & 0x7F) * multiplier;

                    // 检查是否超出最大数据包大小
                    if (remainingLength > MqttProtocolConstants.PacketSizeLimits.MaxPacketSize)
                    {
                        _logger.LogWarning("Packet remaining length {RemainingLength} exceeds maximum allowed size {MaxSize}",
                            remainingLength, MqttProtocolConstants.PacketSizeLimits.MaxPacketSize);
                        return false;
                    }

                    if ((encodedByte & 0x80) == 0)
                        break;

                    multiplier *= 128;
                    byteCount++;
                }

                packetLength = headerLength + remainingLength;

                // 验证总数据包长度的合理性
                if (packetLength > MqttProtocolConstants.PacketSizeLimits.MaxPacketSize || packetLength < headerLength)
                {
                    _logger.LogWarning("Invalid packet length: {PacketLength}, HeaderLength: {HeaderLength}",
                        packetLength, headerLength);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Exception while parsing packet header");
                return false;
            }
        }

        /// <summary>
        /// 检查缓冲区是否包含完整的数据包
        /// </summary>
        /// <param name="buffer">要检查的缓冲区</param>
        /// <returns>如果包含完整数据包则返回 true，否则返回 false</returns>
        public bool HasCompletePacket(ReadOnlySequence<byte> buffer)
        {
            if (!TryParsePacketHeader(buffer, out var packetLength, out _))
                return false;

            return buffer.Length >= packetLength;
        }

        #region 私有辅助方法

        /// <summary>
        /// 验证数据包类型是否有效
        /// </summary>
        /// <param name="packetType">数据包类型</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        private static bool IsValidPacketType(MqttPacketType packetType)
        {
            return packetType switch
            {
                MqttPacketType.Connect => true,
                MqttPacketType.ConnAck => true,
                MqttPacketType.Publish => true,
                MqttPacketType.PubAck => true,
                MqttPacketType.PubRec => true,
                MqttPacketType.PubRel => true,
                MqttPacketType.PubComp => true,
                MqttPacketType.Subscribe => true,
                MqttPacketType.SubAck => true,
                MqttPacketType.Unsubscribe => true,
                MqttPacketType.UnsubAck => true,
                MqttPacketType.PingReq => true,
                MqttPacketType.PingResp => true,
                MqttPacketType.Disconnect => true,
                MqttPacketType.Auth => true,
                _ => false
            };
        }

        /// <summary>
        /// 验证协议名称是否有效
        /// </summary>
        /// <param name="protocolName">协议名称</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        private static bool IsValidProtocolName(string? protocolName)
        {
            if (string.IsNullOrEmpty(protocolName))
                return false;

            return protocolName == MqttProtocolConstants.ProtocolNames.Mqtt311 ||
                   protocolName == MqttProtocolConstants.ProtocolNames.Mqtt50;
        }

        /// <summary>
        /// 验证连接标志是否有效
        /// </summary>
        /// <param name="connectFlags">连接标志</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        private static bool IsValidConnectFlags(byte connectFlags)
        {
            // 检查保留位（第0位）必须为0
            if ((connectFlags & 0x01) != 0)
                return false;

            // 检查密码标志和用户名标志的组合
            var hasUsername = (connectFlags & MqttProtocolConstants.ConnectFlags.UsernameFlag) != 0;
            var hasPassword = (connectFlags & MqttProtocolConstants.ConnectFlags.PasswordFlag) != 0;

            // 如果有密码标志，必须也有用户名标志
            if (hasPassword && !hasUsername)
                return false;

            return true;
        }

        /// <summary>
        /// 验证客户端ID是否有效
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        private static bool IsValidClientId(string? clientId)
        {
            if (string.IsNullOrEmpty(clientId))
                return true; // 空客户端ID在某些情况下是允许的

            if (clientId.Length > MqttProtocolConstants.PacketSizeLimits.MaxClientIdLength)
                return false;

            // 检查是否包含无效字符（根据MQTT规范，客户端ID应该只包含可打印字符）
            foreach (char c in clientId)
            {
                if (c < 0x20 || c > 0x7E) // 非可打印ASCII字符
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 验证遗嘱主题是否有效
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        private static bool IsValidWillTopic(string? topic)
        {
            if (string.IsNullOrEmpty(topic))
                return false;

            if (topic.Length > MqttProtocolConstants.PacketSizeLimits.MaxTopicLength)
                return false;

            // 遗嘱主题不能包含通配符
            if (topic.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel) ||
                topic.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
                return false;

            return true;
        }

        /// <summary>
        /// 验证QoS级别是否有效
        /// </summary>
        /// <param name="qosLevel">QoS级别</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        private static bool IsValidQoSLevel(MqttQoSLevel qosLevel)
        {
            return qosLevel switch
            {
                MqttQoSLevel.AtMostOnce => true,
                MqttQoSLevel.AtLeastOnce => true,
                MqttQoSLevel.ExactlyOnce => true,
                _ => false
            };
        }

        /// <summary>
        /// 验证用户名是否有效
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        private static bool IsValidUsername(string? username)
        {
            if (string.IsNullOrEmpty(username))
                return true;

            return username.Length <= MqttProtocolConstants.PacketSizeLimits.MaxUsernameLength;
        }

        /// <summary>
        /// 验证密码是否有效
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        private static bool IsValidPassword(string? password)
        {
            if (string.IsNullOrEmpty(password))
                return true;

            return password.Length <= MqttProtocolConstants.PacketSizeLimits.MaxPasswordLength;
        }

        #endregion

        #region 私有解析方法

        private MqttConnectPacket ParseConnectPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttConnectPacket();

            try
            {
                _logger.LogDebug("ParseConnectPacket: Starting to parse CONNECT packet. Reader remaining: {Remaining}", reader.Remaining);

                // 输出缓冲区内容用于调试
                var debugBytes = new byte[Math.Min(reader.Remaining, 50)];
                var debugSlice = reader.GetSlice((int)Math.Min(reader.Remaining, 50));
                debugSlice.CopyTo(debugBytes);
                _logger.LogDebug("ParseConnectPacket: Buffer content (first 50 bytes): {BufferHex}", BitConverter.ToString(debugBytes));

                // 解析可变头部
                _logger.LogDebug("ParseConnectPacket: Reading protocol name...");

                // 在读取协议名称之前，检查前两个字节（字符串长度）
                if (reader.Remaining >= 2)
                {
                    var lengthBytes = new byte[2];
                    var lengthSlice = reader.GetSlice(2);
                    lengthSlice.CopyTo(lengthBytes);
                    var protocolNameLength = (ushort)((lengthBytes[0] << 8) | lengthBytes[1]);
                    _logger.LogDebug("ParseConnectPacket: Protocol name length bytes: {Byte0:X2} {Byte1:X2}, calculated length: {Length}",
                        lengthBytes[0], lengthBytes[1], protocolNameLength);
                }

                packet.ProtocolName = reader.ReadString();
                _logger.LogDebug("ParseConnectPacket: Protocol name read: {ProtocolName}, Reader remaining: {Remaining}", packet.ProtocolName, reader.Remaining);

                // 验证协议名称
                if (!IsValidProtocolName(packet.ProtocolName))
                {
                    _logger.LogWarning("Invalid protocol name: {ProtocolName}", packet.ProtocolName);
                    throw new InvalidOperationException($"Invalid protocol name: {packet.ProtocolName}");
                }

                packet.ProtocolVersion = (MqttProtocolVersion)reader.ReadByte();
                var connectFlags = reader.ReadByte();
                packet.KeepAlive = reader.ReadUInt16();

                // 解析连接标志
                packet.CleanSession = (connectFlags & MqttProtocolConstants.ConnectFlags.CleanSessionFlag) != 0;
                var hasWill = (connectFlags & MqttProtocolConstants.ConnectFlags.WillFlag) != 0;
                var hasUsername = (connectFlags & MqttProtocolConstants.ConnectFlags.UsernameFlag) != 0;
                var hasPassword = (connectFlags & MqttProtocolConstants.ConnectFlags.PasswordFlag) != 0;

                // 验证连接标志的有效性
                if (!IsValidConnectFlags(connectFlags))
                {
                    _logger.LogWarning("Invalid connect flags: {ConnectFlags:X2}", connectFlags);
                    throw new InvalidOperationException($"Invalid connect flags: {connectFlags:X2}");
                }

                // MQTT 5.0 属性
                if (packet.ProtocolVersion == MqttProtocolVersion.Version50)
                {
                    _logger.LogInformation("ParseConnectPacket: About to parse properties, Position: {Position}, Remaining: {Remaining}", reader.Position, reader.Remaining);
                    packet.Properties = ParseProperties(ref reader);
                    _logger.LogInformation("ParseConnectPacket: Properties parsed, Position: {Position}, Remaining: {Remaining}", reader.Position, reader.Remaining);
                }

                // 解析有效载荷
                _logger.LogInformation("ParseConnectPacket: Reading client ID. Reader position: {Position}, remaining: {Remaining}", reader.Position, reader.Remaining);
                packet.ClientId = reader.ReadString();
                _logger.LogDebug("ParseConnectPacket: Client ID read: {ClientId}, Reader remaining: {Remaining}", packet.ClientId, reader.Remaining);

                // 验证客户端ID
                if (!IsValidClientId(packet.ClientId))
                {
                    _logger.LogWarning("Invalid client ID: {ClientId}", packet.ClientId);
                    throw new InvalidOperationException($"Invalid client ID: {packet.ClientId}");
                }

                if (hasWill)
                {
                    packet.WillMessage = new MqttWillMessage();

                    // MQTT 5.0 遗嘱属性
                    if (protocolVersion == MqttProtocolVersion.Version50)
                    {
                        packet.WillMessage.Properties = ParseProperties(ref reader);
                    }

                    packet.WillMessage.Topic = reader.ReadString();

                    // 验证遗嘱主题
                    if (!IsValidWillTopic(packet.WillMessage.Topic))
                    {
                        _logger.LogWarning("Invalid will topic: {Topic}", packet.WillMessage.Topic);
                        throw new InvalidOperationException($"Invalid will topic: {packet.WillMessage.Topic}");
                    }

                    packet.WillMessage.Payload = reader.ReadBinaryData();
                    packet.WillMessage.QoSLevel = (MqttQoSLevel)((connectFlags & MqttProtocolConstants.ConnectFlags.WillQoSMask) >> 3);
                    packet.WillMessage.Retain = (connectFlags & MqttProtocolConstants.ConnectFlags.WillRetainFlag) != 0;

                    // 验证遗嘱QoS级别
                    if (!IsValidQoSLevel(packet.WillMessage.QoSLevel))
                    {
                        _logger.LogWarning("Invalid will QoS level: {QoSLevel}", packet.WillMessage.QoSLevel);
                        throw new InvalidOperationException($"Invalid will QoS level: {packet.WillMessage.QoSLevel}");
                    }
                }

                if (hasUsername)
                {
                    packet.Username = reader.ReadString();

                    // 验证用户名
                    if (!IsValidUsername(packet.Username))
                    {
                        _logger.LogWarning("Invalid username: {Username}", packet.Username);
                        throw new InvalidOperationException($"Invalid username: {packet.Username}");
                    }
                }

                if (hasPassword)
                {
                    packet.Password = reader.ReadString();

                    // 验证密码
                    if (!IsValidPassword(packet.Password))
                    {
                        _logger.LogWarning("Invalid password length");
                        throw new InvalidOperationException("Invalid password");
                    }
                }

                return packet;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing CONNECT packet");
                throw new InvalidOperationException("Failed to parse CONNECT packet", ex);
            }
        }

        private MqttConnAckPacket ParseConnAckPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttConnAckPacket();

            var acknowledgeFlags = reader.ReadByte();
            packet.SessionPresent = (acknowledgeFlags & 0x01) != 0;

            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(ref reader);
                }
            }
            else
            {
                packet.ReturnCode = (MqttConnectReturnCode)reader.ReadByte();
            }

            return packet;
        }

        private MqttPublishPacket ParsePublishPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPublishPacket();
            packet.ParseFlags(flags);

            // 解析可变头部
            packet.Topic = reader.ReadString();

            if (packet.QoSLevel > MqttQoSLevel.AtMostOnce)
            {
                packet.PacketIdentifier = reader.ReadUInt16();
            }

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.Properties = ParseProperties(ref reader);
            }

            // 解析有效载荷
            packet.Payload = reader.ReadRemainingBytes();

            return packet;
        }

        private MqttPubAckPacket ParsePubAckPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPubAckPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(ref reader);
                }
            }

            return packet;
        }

        private MqttPubRecPacket ParsePubRecPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPubRecPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(ref reader);
                }
            }

            return packet;
        }

        private MqttPubRelPacket ParsePubRelPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPubRelPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(ref reader);
                }
            }

            return packet;
        }

        private MqttPubCompPacket ParsePubCompPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPubCompPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(ref reader);
                }
            }

            return packet;
        }

        private MqttPingReqPacket ParsePingReqPacket(MqttBinaryReader reader, byte flags)
        {
            return new MqttPingReqPacket();
        }

        private MqttPingRespPacket ParsePingRespPacket(MqttBinaryReader reader, byte flags)
        {
            return new MqttPingRespPacket();
        }

        private MqttProperties? ParseProperties(ref MqttBinaryReader reader)
        {
            _logger.LogInformation("ParseProperties: Starting, Position: {Position}, Remaining: {Remaining}", reader.Position, reader.Remaining);
            var propertiesLength = reader.ReadVariableByteInteger();
            _logger.LogInformation("ParseProperties: Properties length: {PropertiesLength}, Position after length read: {Position}, Remaining: {Remaining}", propertiesLength, reader.Position, reader.Remaining);

            if (propertiesLength == 0)
                return null;

            var properties = new MqttProperties();
            var endPosition = reader.Consumed + propertiesLength;
            _logger.LogInformation("ParseProperties: End position: {EndPosition}, Current consumed: {Consumed}", endPosition, reader.Consumed);

            while (reader.Consumed < endPosition)
            {
                _logger.LogInformation("ParseProperties: Reading property, Position: {Position}, Consumed: {Consumed}, EndPosition: {EndPosition}", reader.Position, reader.Consumed, endPosition);
                var propertyId = (MqttPropertyType)reader.ReadByte();
                _logger.LogInformation("ParseProperties: Property ID: {PropertyId} (0x{PropertyIdHex:X2}), Position: {Position}", propertyId, (byte)propertyId, reader.Position);

                // 根据属性类型读取值
                object value = propertyId switch
                {
                    MqttPropertyType.PayloadFormatIndicator => reader.ReadByte(),
                    MqttPropertyType.MessageExpiryInterval => reader.ReadUInt32(),
                    MqttPropertyType.ContentType => reader.ReadString(),
                    MqttPropertyType.ResponseTopic => reader.ReadString(),
                    MqttPropertyType.CorrelationData => reader.ReadBinaryData(),
                    MqttPropertyType.SubscriptionIdentifier => reader.ReadVariableByteInteger(),
                    MqttPropertyType.SessionExpiryInterval => reader.ReadUInt32(),
                    MqttPropertyType.AssignedClientIdentifier => reader.ReadString(),
                    MqttPropertyType.ServerKeepAlive => reader.ReadUInt16(),
                    MqttPropertyType.AuthenticationMethod => reader.ReadString(),
                    MqttPropertyType.AuthenticationData => reader.ReadBinaryData(),
                    MqttPropertyType.RequestProblemInformation => reader.ReadByte(),
                    MqttPropertyType.WillDelayInterval => reader.ReadUInt32(),
                    MqttPropertyType.RequestResponseInformation => reader.ReadByte(),
                    MqttPropertyType.ResponseInformation => reader.ReadString(),
                    MqttPropertyType.ServerReference => reader.ReadString(),
                    MqttPropertyType.ReasonString => reader.ReadString(),
                    MqttPropertyType.ReceiveMaximum => reader.ReadUInt16(),
                    MqttPropertyType.TopicAliasMaximum => reader.ReadUInt16(),
                    MqttPropertyType.TopicAlias => reader.ReadUInt16(),
                    MqttPropertyType.MaximumQoS => reader.ReadByte(),
                    MqttPropertyType.RetainAvailable => reader.ReadByte(),
                    MqttPropertyType.UserProperty => (reader.ReadString(), reader.ReadString()),
                    MqttPropertyType.MaximumPacketSize => reader.ReadUInt32(),
                    MqttPropertyType.WildcardSubscriptionAvailable => reader.ReadByte(),
                    MqttPropertyType.SubscriptionIdentifierAvailable => reader.ReadByte(),
                    MqttPropertyType.SharedSubscriptionAvailable => reader.ReadByte(),
                    _ => throw new NotSupportedException($"Property type {propertyId} is not supported")
                };

                _logger.LogInformation("ParseProperties: Property value read, Position: {Position}, Consumed: {Consumed}", reader.Position, reader.Consumed);
                properties.Properties[propertyId] = value;
            }

            _logger.LogInformation("ParseProperties: Finished, Position: {Position}, Consumed: {Consumed}, Remaining: {Remaining}", reader.Position, reader.Consumed, reader.Remaining);
            return properties;
        }

        private MqttSubscribePacket ParseSubscribePacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttSubscribePacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.Properties = ParseProperties(ref reader);
            }

            // 解析订阅列表
            while (reader.Remaining > 0)
            {
                var subscription = new MqttSubscription
                {
                    TopicFilter = reader.ReadString()
                };

                var options = reader.ReadByte();
                subscription.ParseOptionsFromByte(options);

                packet.Subscriptions.Add(subscription);
            }

            return packet;
        }

        private MqttSubAckPacket ParseSubAckPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttSubAckPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.Properties = ParseProperties(ref reader);
            }

            // 解析返回码列表
            while (reader.Remaining > 0)
            {
                packet.ReasonCodes.Add((MqttReasonCode)reader.ReadByte());
            }

            return packet;
        }

        private MqttUnsubscribePacket ParseUnsubscribePacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttUnsubscribePacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.Properties = ParseProperties(ref reader);
            }

            // 解析主题过滤器列表
            while (reader.Remaining > 0)
            {
                packet.TopicFilters.Add(reader.ReadString());
            }

            return packet;
        }

        private MqttUnsubAckPacket ParseUnsubAckPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttUnsubAckPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            // MQTT 5.0 属性和原因码
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(ref reader);
                }

                // 解析原因码列表
                while (reader.Remaining > 0)
                {
                    packet.ReasonCodes.Add((MqttReasonCode)reader.ReadByte());
                }
            }

            return packet;
        }

        private MqttDisconnectPacket ParseDisconnectPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttDisconnectPacket();

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(ref reader);
                }
            }

            return packet;
        }

        private MqttAuthPacket ParseAuthPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            if (protocolVersion != MqttProtocolVersion.Version50)
                throw new NotSupportedException("AUTH packet is only supported in MQTT 5.0");

            var packet = new MqttAuthPacket();

            if (reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(ref reader);
                }
            }

            return packet;
        }

        #endregion
    }
}
