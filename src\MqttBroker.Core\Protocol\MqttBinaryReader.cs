using System;
using System.Buffers;
using System.Text;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 二进制数据读取器
    /// </summary>
    public ref struct MqttBinaryReader
    {
        private ReadOnlySequence<byte> _sequence;
        private SequencePosition _position;
        private long _consumed;

        /// <summary>
        /// 初始化 MQTT 二进制读取器
        /// </summary>
        /// <param name="sequence">要读取的字节序列</param>
        public MqttBinaryReader(ReadOnlySequence<byte> sequence)
        {
            _sequence = sequence;
            _position = sequence.Start;
            _consumed = 0;
        }

        /// <summary>
        /// 获取已消费的字节数
        /// </summary>
        public long Consumed => _consumed;

        /// <summary>
        /// 获取剩余的字节数
        /// </summary>
        public long Remaining => _sequence.Length - _consumed;

        /// <summary>
        /// 获取当前位置
        /// </summary>
        public long Position => _consumed;

        /// <summary>
        /// 检查是否还有指定数量的字节可读
        /// </summary>
        /// <param name="count">要检查的字节数</param>
        /// <returns>如果有足够的字节则返回 true，否则返回 false</returns>
        public bool HasBytes(int count)
        {
            return Remaining >= count;
        }

        /// <summary>
        /// 读取一个字节
        /// </summary>
        /// <returns>读取的字节</returns>
        /// <exception cref="InvalidOperationException">当没有足够的字节时抛出</exception>
        public byte ReadByte()
        {
            if (!HasBytes(1))
                throw new InvalidOperationException("Not enough bytes to read a byte");

            var result = _sequence.Slice(_position, 1).FirstSpan[0];
            _position = _sequence.GetPosition(1, _position);
            _consumed++;
            return result;
        }

        /// <summary>
        /// 读取两个字节并组成 ushort (大端序)
        /// </summary>
        /// <returns>读取的 ushort 值</returns>
        /// <exception cref="InvalidOperationException">当没有足够的字节时抛出</exception>
        public ushort ReadUInt16()
        {
            if (!HasBytes(2))
                throw new InvalidOperationException("Not enough bytes to read a UInt16");

            var span = _sequence.Slice(_position, 2).FirstSpan;
            if (span.Length >= 2)
            {
                var result = (ushort)((span[0] << 8) | span[1]);
                _position = _sequence.GetPosition(2, _position);
                _consumed += 2;
                return result;
            }
            else
            {
                // 跨段读取
                var byte1 = ReadByte();
                var byte2 = ReadByte();
                return (ushort)((byte1 << 8) | byte2);
            }
        }

        /// <summary>
        /// 读取四个字节并组成 uint (大端序)
        /// </summary>
        /// <returns>读取的 uint 值</returns>
        /// <exception cref="InvalidOperationException">当没有足够的字节时抛出</exception>
        public uint ReadUInt32()
        {
            if (!HasBytes(4))
                throw new InvalidOperationException("Not enough bytes to read a UInt32");

            var span = _sequence.Slice(_position, 4).FirstSpan;
            if (span.Length >= 4)
            {
                var result = (uint)((span[0] << 24) | (span[1] << 16) | (span[2] << 8) | span[3]);
                _position = _sequence.GetPosition(4, _position);
                _consumed += 4;
                return result;
            }
            else
            {
                // 跨段读取
                var byte1 = ReadByte();
                var byte2 = ReadByte();
                var byte3 = ReadByte();
                var byte4 = ReadByte();
                return (uint)((byte1 << 24) | (byte2 << 16) | (byte3 << 8) | byte4);
            }
        }

        /// <summary>
        /// 读取可变长度整数
        /// </summary>
        /// <returns>读取的整数值</returns>
        /// <exception cref="InvalidOperationException">当格式无效或没有足够的字节时抛出</exception>
        public int ReadVariableByteInteger()
        {
            int value = 0;
            int multiplier = 1;
            int byteCount = 0;

            while (true)
            {
                if (byteCount >= 4)
                    throw new InvalidOperationException("Variable byte integer is too long");

                if (!HasBytes(1))
                    throw new InvalidOperationException("Not enough bytes to read variable byte integer");

                var encodedByte = ReadByte();
                value += (encodedByte & 0x7F) * multiplier;

                if ((encodedByte & 0x80) == 0)
                    break;

                multiplier *= 128;
                byteCount++;
            }

            return value;
        }

        /// <summary>
        /// 读取 UTF-8 字符串（带长度前缀）
        /// </summary>
        /// <returns>读取的字符串</returns>
        /// <exception cref="InvalidOperationException">当没有足够的字节时抛出</exception>
        public string ReadString()
        {
            var length = ReadUInt16();
            if (length == 0)
                return string.Empty;

            // 验证字符串长度的合理性（MQTT协议限制）
            if (length > 65535)
                throw new InvalidOperationException($"String length {length} exceeds maximum allowed size");

            // 验证字符串长度是否合理（防止恶意数据包）
            // 使用 Remaining 而不是 _sequence.Length，因为我们需要检查从当前位置开始的剩余字节数
            if (length > Remaining)
                throw new InvalidOperationException($"String length {length} exceeds remaining buffer size {Remaining}");

            if (!HasBytes(length))
                throw new InvalidOperationException($"Not enough bytes to read string of length {length}. Available: {Remaining}, Required: {length}");

            var stringBytes = ReadBytes(length);

            // 验证UTF-8编码有效性
            try
            {
                return Encoding.UTF8.GetString(stringBytes);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Invalid UTF-8 encoding in string data", ex);
            }
        }

        /// <summary>
        /// 读取二进制数据（带长度前缀）
        /// </summary>
        /// <returns>读取的字节数组</returns>
        /// <exception cref="InvalidOperationException">当没有足够的字节时抛出</exception>
        public byte[] ReadBinaryData()
        {
            var length = ReadUInt16();
            if (length == 0)
                return Array.Empty<byte>();

            // 验证二进制数据长度的合理性
            if (length > Remaining)
                throw new InvalidOperationException($"Binary data length {length} exceeds remaining buffer size {Remaining}");

            return ReadBytes(length);
        }

        /// <summary>
        /// 读取指定数量的字节
        /// </summary>
        /// <param name="count">要读取的字节数</param>
        /// <returns>读取的字节数组</returns>
        /// <exception cref="InvalidOperationException">当没有足够的字节时抛出</exception>
        public byte[] ReadBytes(int count)
        {
            if (count == 0)
                return Array.Empty<byte>();

            if (!HasBytes(count))
                throw new InvalidOperationException($"Not enough bytes to read {count} bytes");

            var result = new byte[count];
            var slice = _sequence.Slice(_position, count);
            slice.CopyTo(result);

            _position = _sequence.GetPosition(count, _position);
            _consumed += count;
            return result;
        }

        /// <summary>
        /// 跳过指定数量的字节
        /// </summary>
        /// <param name="count">要跳过的字节数</param>
        /// <exception cref="InvalidOperationException">当没有足够的字节时抛出</exception>
        public void Skip(int count)
        {
            if (count == 0)
                return;

            if (!HasBytes(count))
                throw new InvalidOperationException($"Not enough bytes to skip {count} bytes");

            _position = _sequence.GetPosition(count, _position);
            _consumed += count;
        }

        /// <summary>
        /// 获取当前位置的字节序列切片
        /// </summary>
        /// <param name="length">切片长度</param>
        /// <returns>字节序列切片</returns>
        public ReadOnlySequence<byte> GetSlice(int length)
        {
            if (!HasBytes(length))
                throw new InvalidOperationException($"Not enough bytes to get slice of length {length}");

            return _sequence.Slice(_position, length);
        }

        /// <summary>
        /// 获取剩余的所有字节
        /// </summary>
        /// <returns>剩余的字节数组</returns>
        public byte[] ReadRemainingBytes()
        {
            var remaining = (int)Remaining;
            if (remaining == 0)
                return Array.Empty<byte>();

            return ReadBytes(remaining);
        }

        /// <summary>
        /// 重置读取器到指定位置
        /// </summary>
        /// <param name="position">要重置到的位置</param>
        public void Reset(long position = 0)
        {
            if (position < 0 || position > _sequence.Length)
                throw new ArgumentOutOfRangeException(nameof(position));

            _position = _sequence.GetPosition(position);
            _consumed = position;
        }
    }
}
